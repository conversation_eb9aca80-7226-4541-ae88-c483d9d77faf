"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainModel = exports.ai = void 0;
const genkit_1 = require("genkit");
const firebase_1 = require("@genkit-ai/firebase");
const v2_1 = require("firebase-functions/v2");
const vertexai_1 = require("@genkit-ai/vertexai");
// Gemini 2.5 Pro model configuration for Vertex AI
const mainModel = vertexai_1.gemini25ProExp0325;
exports.mainModel = mainModel;
const location = "global"; // europe-west1 Frankfurt, Germany
// Configure Genkit instance with Vertex AI for Gemini models
const ai = (0, genkit_1.genkit)({
    plugins: [
        (0, vertexai_1.vertexAI)({
            location: location,
        }),
    ],
    model: mainModel,
});
exports.ai = ai;
// Enable Firebase telemetry for better monitoring
(0, firebase_1.enableFirebaseTelemetry)();
v2_1.logger.info(`Genkit AI initialized with Gemini 2.5 Pro through Vertex AI in ${location}`);
//# sourceMappingURL=genkit-config.js.map