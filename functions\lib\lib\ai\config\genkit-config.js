"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainModel = exports.ai = void 0;
const genkit_1 = require("genkit");
const firebase_1 = require("@genkit-ai/firebase");
const v2_1 = require("firebase-functions/v2");
const modelgarden_1 = require("@genkit-ai/vertexai/modelgarden");
// Claude 3.5 Sonnet model configuration for Vertex AI
const mainModel = modelgarden_1.claude35SonnetV2;
exports.mainModel = mainModel;
const location = "europe-west1"; // europe-west1 Frankfurt, Germany
// Configure Genkit instance with Vertex AI Model Garden for Claude models
const ai = (0, genkit_1.genkit)({
    plugins: [
        (0, modelgarden_1.vertexAIModelGarden)({
            location: location,
            models: [modelgarden_1.claude35SonnetV2],
        }),
    ],
    model: mainModel,
});
exports.ai = ai;
// Enable Firebase telemetry for better monitoring
(0, firebase_1.enableFirebaseTelemetry)();
v2_1.logger.info(`Genkit AI initialized with Claude 3.5 Sonnet through Vertex AI Model Garden in ${location}`);
//# sourceMappingURL=genkit-config.js.map