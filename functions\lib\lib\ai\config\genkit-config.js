"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.claudeSonnet4 = exports.ai = void 0;
const genkit_1 = require("genkit");
const firebase_1 = require("@genkit-ai/firebase");
const v2_1 = require("firebase-functions/v2");
const modelgarden_1 = require("@genkit-ai/vertexai/modelgarden");
const model_1 = require("genkit/model");
const modelgarden_2 = require("@genkit-ai/vertexai/modelgarden");
// Claude Sonnet 4 model configuration for Vertex AI
const location = "europe-west1"; // europe-west1 Frankfurt, Germany
// Define Claude Sonnet 4 model for Vertex AI Model Garden
// Since Claude Sonnet 4 is not in the predefined models, we'll define it
const claudeSonnet4 = (0, model_1.modelRef)({
    name: 'vertexai/claude-sonnet-4',
    info: {
        label: 'Vertex AI Model Garden - Claude Sonnet 4',
        versions: ['claude-sonnet-4@20250514'],
        supports: {
            multiturn: true,
            media: true,
            tools: true,
            systemRole: true,
            output: ['text'],
        },
    },
    configSchema: modelgarden_2.AnthropicConfigSchema,
});
exports.claudeSonnet4 = claudeSonnet4;
// Configure Genkit instance with Vertex AI Model Garden for Claude models
const ai = (0, genkit_1.genkit)({
    plugins: [
        (0, modelgarden_1.vertexAIModelGarden)({
            location: location,
            models: [
                claudeSonnet4,
                modelgarden_1.claude35SonnetV2,
                modelgarden_1.claude35Sonnet,
                modelgarden_1.claude3Sonnet,
                modelgarden_1.claude3Haiku,
                modelgarden_1.claude3Opus
            ],
        }),
    ],
    model: claudeSonnet4,
});
exports.ai = ai;
// Enable Firebase telemetry for better monitoring
(0, firebase_1.enableFirebaseTelemetry)();
v2_1.logger.info(`Genkit AI initialized with Claude Sonnet 4 through Vertex AI Model Garden in ${location}`);
//# sourceMappingURL=genkit-config.js.map