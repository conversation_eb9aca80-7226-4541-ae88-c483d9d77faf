import { genkit } from "genkit";
import { enableFirebaseTelemetry } from "@genkit-ai/firebase";
import { logger } from "firebase-functions/v2";
import {
  vertexAIModelGarden,
  claude35SonnetV2,
  claude35Sonnet,
  claude3Sonnet,
  claude3Haiku,
  claude3Opus
} from "@genkit-ai/vertexai/modelgarden";
import { modelRef } from "genkit/model";
import { AnthropicConfigSchema } from "@genkit-ai/vertexai/modelgarden";

// Claude Sonnet 4 model configuration for Vertex AI
const location = "europe-west1"; // europe-west1 Frankfurt, Germany

// Define Claude Sonnet 4 model for Vertex AI Model Garden
// Since Claude Sonnet 4 is not in the predefined models, we'll define it
const claudeSonnet4 = modelRef({
  name: 'vertexai/claude-sonnet-4',
  info: {
    label: 'Vertex AI Model Garden - Claude Sonnet 4',
    versions: ['claude-sonnet-4@20250514'],
    supports: {
      multiturn: true,
      media: true,
      tools: true,
      systemRole: true,
      output: ['text'],
    },
  },
  configSchema: AnthropicConfigSchema,
});

// Configure Genkit instance with Vertex AI Model Garden for Claude models
const ai = genkit({
  plugins: [
    vertexAIModelGarden({
      location: location,
      models: [
        claudeSonnet4,
        claude35SonnetV2,
        claude35Sonnet,
        claude3Sonnet,
        claude3Haiku,
        claude3Opus
      ],
    }),
  ],
  model: claudeSonnet4,
});

// Enable Firebase telemetry for better monitoring
enableFirebaseTelemetry();

logger.info(`Genkit AI initialized with Claude Sonnet 4 through Vertex AI Model Garden in ${location}`);

// Export the configured AI instance for use across the application
export { ai, claudeSonnet4 };
