import { AnthropicVertex } from "@anthropic-ai/vertex-sdk";
import { genkit } from "genkit";
import { enableFirebaseTelemetry } from "@genkit-ai/firebase";
import { logger } from "firebase-functions/v2";

// Claude Sonnet 4 model configuration
const mainModel = "claude-sonnet-4@20250514";
const location = "europe-west1"; // europe-west1 Frankfurt, Germany

// Initialize Anthropic Vertex client
const anthropicClient = new AnthropicVertex({
  region: location,
  // projectId will be automatically detected from environment or metadata service
});

// Create a wrapper function for Genkit compatibility
const claudeWrapper = async (prompt: string, options?: any) => {
  try {
    const response = await anthropicClient.messages.create({
      model: mainModel,
      max_tokens: options?.maxTokens || 1000,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      ...options,
    });

    return response.content[0]?.type === 'text' ? response.content[0].text : '';
  } catch (error) {
    logger.error('Claude API error:', error);
    throw error;
  }
};

// Configure Genkit instance with Claude wrapper
const ai = genkit({
  plugins: [],
  // Note: Genkit doesn't have native Anthropic support yet,
  // so we'll use the wrapper function directly
});

// Enable Firebase telemetry for better monitoring
enableFirebaseTelemetry();

logger.info(`Genkit AI initialized with Claude Sonnet 4 in ${location}`);

// Export the configured AI instance and Claude client for use across the application
export { ai, mainModel, anthropicClient, claudeWrapper };
