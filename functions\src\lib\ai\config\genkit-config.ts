import { genkit } from "genkit";
import { enableFirebaseTelemetry } from "@genkit-ai/firebase";
import { logger } from "firebase-functions/v2";
import {
  vertexAIModelGarden,
  claude35SonnetV2,
} from "@genkit-ai/vertexai/modelgarden";

// Claude 3.5 Sonnet model configuration for Vertex AI
const mainModel = claude35SonnetV2;
const location = "europe-west1"; // europe-west1 Frankfurt, Germany

// Configure Genkit instance with Vertex AI Model Garden for Claude models
const ai = genkit({
  plugins: [
    vertexAIModelGarden({
      location: location,
      models: [claude35SonnetV2],
    }),
  ],
  model: mainModel,
});

// Enable Firebase telemetry for better monitoring
enableFirebaseTelemetry();

logger.info(`Genkit AI initialized with Claude 3.5 Sonnet through Vertex AI Model Garden in ${location}`);

// Export the configured AI instance for use across the application
export { ai, mainModel };
