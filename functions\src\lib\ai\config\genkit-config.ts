import { genkit } from "genkit";
import { enableFirebaseTelemetry } from "@genkit-ai/firebase";
import { logger } from "firebase-functions/v2";
import { vertexAI, gemini25ProExp0325 } from "@genkit-ai/vertexai";

// Gemini 2.5 Pro model configuration for Vertex AI
const mainModel = gemini25ProExp0325;
const location = "global"; // europe-west1 Frankfurt, Germany

// Configure Genkit instance with Vertex AI for Gemini models
const ai = genkit({
  plugins: [
    vertexAI({
      location: location,
    }),
  ],
  model: mainModel,
});

// Enable Firebase telemetry for better monitoring
enableFirebaseTelemetry();

logger.info(`Genkit AI initialized with Gemini 2.5 Pro through Vertex AI in ${location}`);

// Export the configured AI instance for use across the application
export { ai, mainModel };
