import { vertexAI} from "@genkit-ai/vertexai";
import {} from @genkit-ai/anthropic

import { genkit } from "genkit";
import { enableFirebaseTelemetry } from "@genkit-ai/firebase";
import { logger } from "firebase-functions/v2";

const mainModel = gemini25FlashPreview0417;
const location = "europe-west1"; // europe-west1 Frankfurt, Germany
// Configure Genkit instance with Vertex AI in European region
const ai = genkit({
  plugins: [
    vertexAI({
      location: location,
    }),
  ],
  model: mainModel,
});

// Enable Firebase telemetry for better monitoring
enableFirebaseTelemetry();

logger.info(`Genkit AI initialized with Vertex AI in ${location}`);

// Export the configured AI instance for use across the application
export { ai, mainModel };
