import * as functions from "firebase-functions/v1";
import { logger } from "firebase-functions/v2";
import { ai, mainModel } from "../config/genkit-config";
import { UserStorySchema, UserStory } from "../schemas/story-schema";
import { createStoryPrompt } from "../prompts/story-prompts";

/**
 * Transforms user feedback into a structured user story
 */
// Instead of exporting the function directly, create it to be exported from index.ts
const generateUserStoryFunction = async (data: any, context: any) => {
  const requestId = Date.now().toString();

  logger.debug("Story generation started", { 
    requestId,
    functionName: "generateUserStoryFunction" 
  });

  try {
    // Authentication is automatically handled by onCall
    if (!context || !context.auth) {
      logger.warn("Authentication missing", { requestId });
      throw new functions.https.HttpsError(
        "unauthenticated",
        "Authentication required"
      );
    }

    const userId = context.auth.uid;
    logger.debug("User authenticated", { requestId, userId });

    // Basic validation
    if (!data.feedbackText || typeof data.feedbackText !== "string") {
      logger.warn("Invalid feedback text", { requestId, userId, feedbackProvided: !!data.feedbackText });
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Feedback text is required"
      );
    }

    const projectContext = data.projectContext || "";
    const issuesContext = data.issuesContext || "";

    logger.debug("Input data validated", {
      requestId,
      userId,
      feedbackLength: data.feedbackText.length,
      feedbackPreview: data.feedbackText.substring(0, 100) + (data.feedbackText.length > 100 ? "..." : ""),
      projectContextLength: projectContext.length,
      issuesContextLength: issuesContext.length
    });

    logger.info("Generating user story from feedback", {
      requestId,
      userId,
      hasProjectContext: !!projectContext,
      hasIssuesContext: !!issuesContext
    });

    logger.debug("Creating prompt", { requestId, userId });
    const prompt = createStoryPrompt(data.feedbackText, projectContext, issuesContext);
    logger.debug("Prompt created", { 
      requestId, 
      userId, 
      promptLength: typeof prompt === 'string' ? prompt.length : 'complex-prompt-object' 
    });

    logger.debug("Calling AI model", { 
      requestId, 
      userId, 
      model: mainModel,
      temperature: 0.7,
      maxOutputTokens: 1024
    });

    const { output } = await ai.generate({
      model: mainModel,
      prompt,
      output: { schema: UserStorySchema },
      config: {
        temperature: 0.7,
        maxOutputTokens: 1024,
        generationConfig: {
          responseMimeType: "application/json"
        }
      },
    });

    const userStory = output as UserStory;
    
    logger.debug("AI response received", {
      requestId,
      userId,
      responseReceived: !!output,
      storyTitle: userStory.title,
      storyLength: JSON.stringify(userStory).length,
      storyFields: Object.keys(userStory)
    });

    logger.info("User story generated successfully", {
      requestId,
      userId,
      storyTitle: userStory.title,
    });

    return output;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorType = error instanceof Error ? error.constructor.name : 'Unknown';

    logger.debug("Exception details", {
      requestId,
      errorType,
      errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    logger.error("Error generating user story", {
      requestId,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    // onCall automatically handles error forwarding to the client
    if (error instanceof functions.https.HttpsError) {
      throw error;
    } else {
      throw new functions.https.HttpsError("internal", errorMessage);
    }
  }
};

// Export the handler function to be used in index.ts
export { generateUserStoryFunction };
