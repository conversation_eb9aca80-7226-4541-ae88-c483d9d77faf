import { ai } from './lib/ai/config/genkit-config';

async function testClaude() {
  try {
    console.log('Testing Claude 3.5 Sonnet integration...');
    
    const response = await ai.generate({
      prompt: 'Hello! Please confirm you are Claude 3.5 Sonnet and briefly describe your capabilities.',
      config: {
        maxOutputTokens: 200,
        temperature: 0.7,
      },
    });

    console.log('Claude Response:', response.text);
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testClaude();
}

export { testClaude };
