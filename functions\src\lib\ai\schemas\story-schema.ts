import { z } from "genkit";

// Schema for structured user story output
export const UserStorySchema = z.object({
  title: z.string(),
  role: z.string(),
  goal: z.string(),
  benefit: z.string(),
  formattedStory: z.string(),
  // New evaluation fields
  feasibility: z.number().min(1).max(5),
  complexity: z.number().min(1).max(10),
  priority: z.enum(["Critical", "High", "Medium", "Low"]),
  featureCategory: z.string(),
});

// User story interface matching the required format
export interface UserStory {
  title: string;
  role: string;
  goal: string;
  benefit: string;
  formattedStory: string;
  // New evaluation fields
  feasibility: number;
  complexity: number;
  priority: "Critical" | "High" | "Medium" | "Low";
  featureCategory: string;
} 